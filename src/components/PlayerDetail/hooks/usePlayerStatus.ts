import { useMemo } from 'react';
import { useCachedFixtures } from '../../../hooks/useCachedData';
import { CorePlayer } from '../../../models/player';
import { Team } from '../../../models/team';
import { calculateCurrentEnergy, calculateEnergyByNextMatch } from '../../../utils/PlayerUtils';
import { formatCurrencyShort } from '../../../utils/utils';

export interface PlayerStatus {
  formattedValue: string;
  currentEnergy: number | undefined;
  energyByNextMatch?: number | undefined;
  isInjured: boolean;
  isSuspended: boolean;
  isRetiring: boolean;
  isPlayerInUserTeam: boolean;
  isAuctionPlayer: boolean;
  isTransferListed: boolean;
}

export const usePlayerStatus = (player: CorePlayer, userTeam?: Team, team?: Team): PlayerStatus => {
  const { getNextFixture } = useCachedFixtures(player?.gameworldId, team?.league.id, team?.teamId);

  return useMemo(() => {
    const formattedValue = formatCurrencyShort(player.value);
    const now = Date.now();
    const isInjured = player.injuredUntil && player.injuredUntil > now;
    const isSuspended = !!player.suspendedForGames && player.suspendedForGames > 0;
    const isPlayerInUserTeam = userTeam && player.teamId === userTeam.teamId;
    const isAuctionPlayer = player.teamId === '' || player.isTransferListed;
    const isRetiring = player.retiringAtEndOfSeason;
    const isTransferListed = player.isTransferListed;

    const currentEnergy =
      player.energy && player.lastMatchPlayed
        ? calculateCurrentEnergy(player.attributes.stamina, player.energy, player.lastMatchPlayed)
        : undefined;

    const energyByNextMatch =
      player.energy && player.lastMatchPlayed && getNextFixture
        ? calculateEnergyByNextMatch(
            player.attributes.stamina,
            player.energy,
            player.lastMatchPlayed,
            getNextFixture.date
          )
        : undefined;

    return {
      formattedValue,
      currentEnergy,
      isInjured: !!isInjured,
      isSuspended,
      isRetiring: !!isRetiring,
      isPlayerInUserTeam: !!isPlayerInUserTeam,
      isTransferListed,
      isAuctionPlayer,
      energyByNextMatch,
    };
  }, [player, userTeam, team]);
};
