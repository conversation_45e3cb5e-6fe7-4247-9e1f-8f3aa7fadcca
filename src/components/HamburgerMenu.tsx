import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Modal, Platform, ScrollView, TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { useAuth } from '../context/AuthContext';
import { useDeviceShake } from '../hooks/useDeviceShake';
import { logger } from '../utils/logger';
import { FeedbackModal } from './FeedbackModal';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface MenuItem {
  name: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  href: string;
}

interface HamburgerMenuProps {
  loading?: boolean;
}

const HamburgerButton = styled.TouchableOpacity`
  padding-right: 20px;
  z-index: 1;

  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const ModalContainer = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
`;

const MenuContainer = styled.View`
  width: 280px;
  height: 100%;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 60px 20px 20px;
  position: absolute;
  right: 0;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: -2px 0px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

const MenuScrollContainer = styled.ScrollView`
  flex: 1;
`;

const MenuHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const MenuTitle = styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const CloseButton = styled.TouchableOpacity`
  padding: 5px;
`;

const MenuItem = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  padding: 15px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border}20;
`;

const MenuItemIcon = styled.View`
  width: 40px;
  margin-right: 15px;
`;

const MenuItemText = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
`;

const CloseArea = styled.TouchableOpacity`
  flex: 1;
`;

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({ loading = false }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isFeedbackVisible, setIsFeedbackVisible] = useState(false);
  const router = useRouter();
  const { logout } = useAuth();

  // Device shake detection to open feedback
  useDeviceShake({
    onShake: () => {
      if (!isFeedbackVisible) {
        setIsFeedbackVisible(true);
      }
    },
  });

  const menuItems: MenuItem[] = [
    { name: 'Home', icon: 'home', href: '/' },
    { name: 'Team', icon: 'group', href: '/team' },
    { name: 'League', icon: 'emoji-events', href: '/league' },
    { name: 'Fixtures', icon: 'event', href: '/fixtures' },
    { name: 'Transfers', icon: 'swap-horiz', href: '/transfers' },
    { name: 'Training', icon: 'fitness-center', href: '/training' },
    { name: 'Finances', icon: 'account-balance-wallet', href: '/finances' },
    { name: 'Club Shop', icon: 'shopping-cart', href: '/club-shop' },
    { name: 'Team Settings', icon: 'settings', href: '/team-settings' },
    { name: 'Profile', icon: 'person', href: '/manager-profile' },
    { name: 'Notifications', icon: 'notifications-active', href: '/notification-settings' },
  ];

  const handleFeedbackPress = () => {
    setIsVisible(false);
    setIsFeedbackVisible(true);
  };

  const handleMenuItemPress = (href: string) => {
    setIsVisible(false);
    //if (!loading) {
    logger.debug('Navigating to:', href);
    router.push(href);
    //}
  };

  const handleLogout = async () => {
    setIsVisible(false);
    await logout();
  };

  return (
    <>
      <HamburgerButton onPress={() => setIsVisible(true)} disabled={loading}>
        <MaterialIcons name="menu" size={24} color={loading ? '#999' : undefined} />
      </HamburgerButton>

      <Modal
        visible={isVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsVisible(false)}
      >
        <ModalContainer>
          <CloseArea onPress={() => setIsVisible(false)} />
          <MenuContainer>
            <MenuHeader>
              <MenuTitle>Menu</MenuTitle>
              <CloseButton onPress={() => setIsVisible(false)}>
                <MaterialIcons name="close" size={24} />
              </CloseButton>
            </MenuHeader>

            <MenuScrollContainer showsVerticalScrollIndicator={false}>
              {menuItems.map((item) => (
                <MenuItem
                  key={item.name}
                  onPress={() => handleMenuItemPress(item.href)}
                  disabled={loading}
                  style={loading ? { opacity: 0.5 } : {}}
                >
                  <MenuItemIcon>
                    <MaterialIcons
                      name={item.icon}
                      size={24}
                      color={loading ? '#999' : undefined}
                    />
                  </MenuItemIcon>
                  <MenuItemText>{item.name}</MenuItemText>
                </MenuItem>
              ))}

              {/* Feedback option */}
              <MenuItem
                onPress={handleFeedbackPress}
                disabled={loading}
                style={loading ? { opacity: 0.5 } : {}}
              >
                <MenuItemIcon>
                  <MaterialIcons name="feedback" size={24} color={loading ? '#999' : undefined} />
                </MenuItemIcon>
                <MenuItemText>Send Feedback</MenuItemText>
              </MenuItem>

              {/* Logout option */}
              <MenuItem
                onPress={handleLogout}
                disabled={loading}
                style={loading ? { opacity: 0.5 } : {}}
              >
                <MenuItemIcon>
                  <MaterialIcons name="logout" size={24} color={loading ? '#999' : undefined} />
                </MenuItemIcon>
                <MenuItemText>Logout</MenuItemText>
              </MenuItem>
            </MenuScrollContainer>
          </MenuContainer>
        </ModalContainer>
      </Modal>

      <FeedbackModal visible={isFeedbackVisible} onClose={() => setIsFeedbackVisible(false)} />
    </>
  );
};
