import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, Image, View } from 'react-native';
import styled from 'styled-components/native';
import { useManager } from '../../context/ManagerContext';
import { CorePlayer } from '../../models/player';
import { getAttributesToShow } from '../../utils/PlayerUtils';
import { StyledProps } from '../Common';
import { usePlayerStatus } from '../PlayerDetail/hooks/usePlayerStatus';
import {
  AttributeGroup,
  AttributeLabel,
  AttributesContainer,
  AttributeValue,
  CardHeader,
  EnergyContainer,
  EnergyText,
  PlayerName,
  PlayerValue,
  StatusIconContainer,
} from './PlayerRowStyles';

interface PlayerInfoProps {
  player: CorePlayer;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  showImages?: boolean; // Whether to show injury/suspension images or icons
  showValue?: boolean;
}

// Styled component for energy value with dynamic color logic and React Native text shadow for readability
const EnergyValue = styled.Text<{ energy: number }>`
  color: ${({ energy }) => {
    const clamped = Math.max(0, Math.min(100, energy));
    const r = Math.round(255 * (1 - clamped / 100));
    const g = Math.round(200 * (clamped / 100));
    return `rgb(${r},${g},0)`;
  }};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-shadow-color: ${(props) => props.theme.colors.shadow};
  text-shadow-offset: 2px 2px;
  text-shadow-radius: 2px;
`;

const PlayerInfo: React.FC<PlayerInfoProps> = ({
  player,
  positionFilter = 'All',
  showImages = false,
  showValue = true,
}) => {
  const {
    formattedValue,
    currentEnergy,
    energyByNextMatch,
    isInjured,
    isSuspended,
    isRetiring,
    isTransferListed,
  } = usePlayerStatus(player);
  const { manager } = useManager();

  const attributesToShow = getAttributesToShow(player, positionFilter);
  const showAttributes = attributesToShow.length > 0;

  // Check screen width to determine if we're on mobile
  const { width } = Dimensions.get('window');
  const isMobile = width < 768;

  return (
    <View style={{ flex: 1 }}>
      <CardHeader hasAttributes={showAttributes} isMobile={isMobile}>
        {(isInjured || isSuspended || isRetiring || isTransferListed) && (
          <StatusIconContainer>
            {isInjured &&
              (showImages ? (
                <Image
                  source={require('../../../assets/injury.png')}
                  style={{ width: 16, height: 16 }}
                  resizeMode="contain"
                />
              ) : (
                <MaterialIcons name="healing" size={16} color="#ffffff" />
              ))}
            {isSuspended && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                {showImages ? (
                  <Image
                    source={require('../../../assets/redcard.png')}
                    style={{ width: 16, height: 16 }}
                    resizeMode="contain"
                  />
                ) : (
                  <MaterialCommunityIcons name="card" size={16} color="#f44336" />
                )}
              </View>
            )}
            {isRetiring && showImages && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                <Image
                  source={require('../../../assets/retiring.png')}
                  style={{ width: 16, height: 16 }}
                  resizeMode="contain"
                />
              </View>
            )}
            {isTransferListed && (
              <View
                style={{ position: 'relative', alignItems: 'center', justifyContent: 'center' }}
              >
                <MaterialCommunityIcons name="swap-horizontal" size={16} color="#000000" />
              </View>
            )}
          </StatusIconContainer>
        )}
        <PlayerName
          isMobile={isMobile}
          isUnavailable={isInjured || isSuspended}
          style={{ flex: showAttributes ? 1 : undefined, textAlign: 'left' }}
        >
          {`${player.firstName} ${player.surname}`}
        </PlayerName>
        {showValue && <PlayerValue style={{ marginLeft: 8 }}>{formattedValue}</PlayerValue>}
      </CardHeader>
      {!showAttributes && (
        <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
          <EnergyContainer>
            {currentEnergy && energyByNextMatch && (
              <EnergyText>
                Energy:{' '}
                {currentEnergy < 100 ? (
                  <>
                    <EnergyValue energy={currentEnergy}>{`${currentEnergy}%`}</EnergyValue>
                    {' → '}
                    <EnergyValue energy={energyByNextMatch}>{`${energyByNextMatch}%`}</EnergyValue>
                  </>
                ) : (
                  <EnergyValue energy={currentEnergy}>{`${currentEnergy}%`}</EnergyValue>
                )}
              </EnergyText>
            )}
          </EnergyContainer>
        </AttributesContainer>
      )}
      {showAttributes && (
        <AttributesContainer isMobile={isMobile} style={{ alignSelf: 'flex-start' }}>
          {attributesToShow.map((attr, index) => (
            <AttributeGroup key={index}>
              <AttributeLabel>{attr.label}:</AttributeLabel>
              <AttributeValue>
                {manager?.role !== 'admin' ? Math.floor(attr.value) : attr.value.toFixed(2)}
              </AttributeValue>
            </AttributeGroup>
          ))}
        </AttributesContainer>
      )}
    </View>
  );
};

export default PlayerInfo;
